package com.ruoyi.system.api.domain.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 标签返回对象VO
 *
 * <AUTHOR>
 * @date 2024-05-20
 */
@ApiModel(value = "标签返回对象VO")
@Data
public class TagListVO implements Serializable {
    private static final long serialVersionUID = -3023703529185476098L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 父标签id
     */
    @ApiModelProperty(value = "父标签id")
    private Long parentId = 0L;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String name;


    /**
     * 标签英文名称
     */
    @ApiModelProperty(value = "标签英文名称")
    private String englishName;

    @ApiModelProperty(value = "排序")
    private Long sort;

    /**
     * 状态（0:禁用，1:启用）
     */
    @ApiModelProperty(value = "状态", notes = "0:禁用，1:启用")
    @Excel(name = "状态", readConverterExp = "0:禁用，1:启用")
    private String status;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 创建者
     */
    @JsonIgnore
    private Long createBy;
    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private UserVO createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    private List<TagListVO> children = new ArrayList<>();
}
