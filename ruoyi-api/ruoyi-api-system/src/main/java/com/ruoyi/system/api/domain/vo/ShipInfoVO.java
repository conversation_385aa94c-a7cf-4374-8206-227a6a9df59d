package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/31 17:07
 */
@ApiModel(value = "寄件信息VO")
@Data
public class ShipInfoVO implements Serializable {

    private static final long serialVersionUID = 8351563585637927269L;
    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    private String recipient;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty(value = "州")
    private String state;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipcode;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    /**
     * 物流信息
     */
    @ApiModelProperty(value = "物流信息")
    private List<OrderLogisticVO> orderLogistic;
}
