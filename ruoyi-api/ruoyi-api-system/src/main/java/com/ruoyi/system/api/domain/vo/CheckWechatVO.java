package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :检查微信
 * @create :2024-08-01 17:09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckWechatVO implements Serializable {
    private static final long serialVersionUID = -9090214944542337125L;
    @ApiModelProperty("状态：0-自定义异常，1-已是该公司子账号，2-已是其它公司子账号，未解绑权限，3-您已是平台主账号，4企业主账号会员已到期，无法加入，5-已申请子账号，无法再次申请，6-有效账号，7-无登录账号，8-用户未授权")
    private Integer status;
    @ApiModelProperty("提示信息")
    private String message;
    @ApiModelProperty("申请商家名称")
    private String applyBusinessName;
    @ApiModelProperty("已存在商家名称")
    private String businessName;

    /**
     * 未支付的会员订单
     */
    @ApiModelProperty("未支付的会员订单")
    private Collection<String> unPayedMemberOrderNums;
}

