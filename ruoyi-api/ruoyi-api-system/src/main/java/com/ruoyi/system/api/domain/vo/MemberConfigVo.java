package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-08-02 17:42
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberConfigVo implements Serializable {
    private static final long serialVersionUID = -3488508152154321475L;

    @ApiModelProperty(value = "加赠时间")
    private Integer presentedTime;

    @ApiModelProperty(value = "加赠开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date presentedStartDate;

    @ApiModelProperty(value = "加赠结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date presentedEndDate;

    @ApiModelProperty(value = "是否加赠:0-否，1-是")
    private Integer status;
}
