
package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.logistic.LogisticFollowNotifyDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.ModelUpdateAddressDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.entity.order.OrderVideoRefund;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.ModelBasicDataVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.order.ModelOrderVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import com.ruoyi.system.api.factory.RemoteOrderFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 模特服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteOrderService", value = ServiceNameConstants.ORDER_SERVICE, fallbackFactory = RemoteOrderFallbackFactory.class)
public interface RemoteOrderService {

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @return 模特id
     */
    @PostMapping("/order/inner/query-order-model")
    public List<Long> queryOrderModel(@RequestBody Collection<Long> modelId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取订单统计
     *
     * @param orderVideoStatisticsDTO
     * @return
     */
    @PostMapping("/order/orderVideoStatistics")
    public OrderVideoStatisticsVO orderVideoStatistics(@RequestBody OrderVideoStatisticsDTO orderVideoStatisticsDTO);

    /**
     * 获取订单统计相详情
     *
     * @param orderVideoStatisticsDTO
     * @return
     */
    @PostMapping("/order/orderVideoStatisticsDetail")
    public List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(@RequestBody OrderVideoStatisticsDTO orderVideoStatisticsDTO);

    /**
     * 批量更新视频订单的对接人
     */
    @PostMapping("/order/inner/update-order-contact")
    Boolean updateOrderVideoContact(@RequestBody UpdateOrderContactDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 接收抓取亚马逊图片更新视频订单
     */
    @PostMapping(value = "/order/inner/update-batch-order-video-product-pic")
    public void updateBatchOrderVideoProductPic(@RequestBody List<UpdateBatchOrderVideoProductPicDTO> dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 接收抓取亚马逊图片更新购物车订单
     */
    @PostMapping(value = "/order/inner/update-batch-order-cart-product-pic")
    public void updateBatchOrderCartProductPic(@RequestBody List<UpdateBatchOrderVideoProductPicDTO> dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    @PostMapping("/order/inner/get-model-order-count")
    public List<ModelOrderVO> getModelOrderCount(@RequestBody Collection<Long> modelIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 获取模特超时率、售后率
     */
    @GetMapping("/order/inner/get-model-overtime-rate-and-after-sale-rate")
    public List<OrderModelTimeoutVO> getModelOvertimeRateAndAfterSaleRate(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取订单有效数量
     * * @param bizUserId
     * @param source
     * @return
     */
    @GetMapping("/order/inner/getValidOrderCount")
    Long getValidOrderCount(@RequestParam("bizUserId") Long bizUserId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 新增活动修改记录
     * @param promotionActivityAmendmentRecord
     * @param source
     * @return
     */
    @PostMapping("/order/inner/promotionActivity/savePromotionActivityAmendmentRecord")
    String savePromotionActivityAmendmentRecord(@RequestBody PromotionActivityAmendmentRecord promotionActivityAmendmentRecord, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取会员有效订单
     */
    @GetMapping("/order/inner/get-valid-order-list")
    List<OrderMember> getValidOrderMemberList(@RequestParam("bizUserId") Long bizUserId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取未取消订单数量
     * @param businessId
     * @param source
     * @return
     */
    @GetMapping("/order/inner/getUnCancelOrderCount")
    Long getUnCancelOrderCount(@RequestParam("businessId") Long businessId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 更新预选模特列表为已淘汰
     */
    @PostMapping("/match/out-preselect-model")
    Boolean outPreselectModel(@RequestBody List<OutPreselectModelDTO> dtoList, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 添加预选模特列表查询匹配单下非淘汰的模特
     */
    @PostMapping("/match/select-normal-preselect-model-by-match-id")
    Set<Long> selectNormalPreselectModelByMatchId(@RequestParam(value = "matchId") Long matchId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**+
     * 获取订单明细基础数据
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/order/inner/getBasePayDetailVOS")
    public List<OrderPayDetailVO> getBasePayDetailVOS(@RequestBody OrderPayDetailDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**+
     * 保存订单支付流水
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/order/inner/saveOrderPayLog")
    public String saveOrderPayLog(@RequestBody OrderPayLogDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 禁用会员发票
     * @param businessId
     * @param source
     * @return
     */
    @PostMapping("/order/inner/banMemberInvoice")
    public String banMemberInvoice(@RequestParam(value = "businessId") Long businessId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 保存订单收款账号
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/order/inner/saveOrderPayeeAccount")
    public String saveOrderPayeeAccount(@RequestBody OrderPayAccountDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     *  获取收款账号列表
     * @param orderNums
     * @param source
     * @return
     */
    @PostMapping("/order/inner/queryOrderPayeeAccountListByOrderNums")
    public List<OrderPayeeAccount> queryOrderPayeeAccountListByOrderNums(@RequestBody Collection<String> orderNums, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    @PostMapping("/order/inner/update-issue-id")
    String updateIssueId(@RequestBody UpdateIssueIdDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 清楚购物车意向模特
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/order/inner/clearVideoCartIntentionModelId")
    String clearVideoCartIntentionModelId(@RequestBody ClearVideoCartIntentionModelDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 提现申请通过后 对发票的处理
     */
    @PostMapping("/invoice/withdrawal-success")
    Boolean withdrawalSuccess(@RequestBody List<WithdrawalSuccessDTO> dtoList, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/order/getOrderVideoRefundList")
    List<OrderVideoRefund> getOrderVideoRefundList(@RequestBody List<String> numbers, @RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 获取已支付的视频订单
     */
    @GetMapping("/order/inner/getPayedOrderList")
    List<Order> getPayedOrderList(@RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 获取提交凭证 未审核订单
     * @param inner
     * @return
     */
    @GetMapping("/order/inner/getPayedUnCheckOrderList")
    List<Order> getPayedUnCheckOrderList(@RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 通过视频订单ID获取被商家驳回的模特ID
     */
    @GetMapping("/match/select-reject-model-id-by-video-id")
    Set<Long> selectRejectModelIdByVideoId(@RequestParam("videoId") Long videoId, @RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 通过商家ID查询商家是否有进行中或者交易完成的视频订单
     */
    @GetMapping("/order/inner/has-valid-video-order-by-business-id")
    Boolean hasValidVideoOrderByBusinessId(@RequestParam("businessId") Long businessId, @RequestParam(value = "startTime", required = false) String startTime, @RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 关闭所有订单
     * @param orderNums
     * @param source
     * @return
     */
    @PostMapping("/order/inner/closeAllOrder")
    String closeAllOrder(@RequestBody Collection<String> orderNums, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 关闭所有订单
     * @param orderNums
     * @param source
     * @return
     */
    @PostMapping("/order/inner/getOrderListByOrderNums")
    List<Order> getOrderListByOrderNums(@RequestBody Collection<String> orderNums, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 模特数据统计-获取模特基础数据
     */
    @GetMapping("/order/inner/get-model-basic-data")
    ModelBasicDataVO getModelBasicData(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 模特数据统计-模特接单排行榜
     */
    @GetMapping("/order/inner/get-model-order-ranking")
    List<ModelOrderRankingInfo> getModelOrderRanking(@RequestParam("date") String date, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * （一次性）初始化模特数据-模特接单排行榜
     */
    @PostMapping("/order/inner/get-model-order-rankings")
    Map<String, List<ModelOrderRankingInfo>> getModelOrderRankings(@RequestBody List<String> collect, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 同步订单物流跟进状态
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/order/inner/logisticFollowNotify")
    String logisticFollowNotify(@RequestBody LogisticFollowNotifyDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 同步订单物流跟进状态
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/order/inner/modelUpdateAddress")
    String modelUpdateAddress(@RequestBody ModelUpdateAddressDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 添加分发模特列表查询匹配单下的模特ID
     */
    @PostMapping("/match/select-preselect-model-by-match-id")
    Set<Long> selectPreselectModelIdsByMatchId(@RequestParam("matchId") Long matchId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 模特数据-模特排单情况
     */
    @PostMapping("/order/inner/model-order-scheduled-data")
    List<PieChartVO> getModelOrderScheduledData(@RequestBody Collection<Long> modelIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询模特数据表列表
     */
    @PostMapping("/order/inner/model-data-table/list")
    List<ModelDataTableListVO> selectModelDataTableListByCondition(@RequestBody ModelDataTableListDTO modelDataTableListDTO, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 获取订单第一次进入待匹配时间
     */
    @PostMapping("/order/inner/getOrderFirstMatchTime")
    Date getOrderFirstMatchTime(Long orderId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
