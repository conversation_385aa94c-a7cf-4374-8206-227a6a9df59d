package com.ruoyi.system.api.domain.dto.biz.channel;

import com.ruoyi.common.core.enums.LandingFormEnum;
import com.ruoyi.common.core.enums.MarketingPlatformEnum;
import com.ruoyi.common.core.validated.CommonValidatedGroup;
import com.ruoyi.common.core.validated.annotation.EnumValid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
public class MarketingChannelDTO implements Serializable {
    private static final long serialVersionUID = 2278698086092076321L;
    /**
     * 主键
     */
    @NotNull(message = "[主键]不能为空", groups = CommonValidatedGroup.EditValidatedGroup.class)
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 市场渠道平台
     */
    @NotNull(message = "[投放平台]不能为空", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    @Null(message = "[投放平台]不允许修改", groups = CommonValidatedGroup.EditValidatedGroup.class)
    @ApiModelProperty(value = "市场渠道平台 (1=百度,2=小红书,3=穿山甲,4=优量汇,5=今日头条,6=腾讯)")
    @EnumValid(enumClass = MarketingPlatformEnum.class, message = "[投放平台]输入错误")
    private Integer marketingPlatform;

    /**
     * 投流名称
     */
    @NotBlank(message = "[投流名称]不能为空")
    @Size(max = 27, message = "[投流名称]编码长度不能超过27个字符")
    @ApiModelProperty(value = "市场渠道名称", required = true)
    private String marketingChannelName;

    /**
     * 落地形式
     */
    @NotNull(message = "[落地形式]不能为空", groups = CommonValidatedGroup.SaveValidatedGroup.class)
    @ApiModelProperty(value = "落地形式（1:官网首页，2:添加企微客服）", required = true)
    @EnumValid(enumClass = LandingFormEnum.class, message = "[落地形式]输入错误")
    private Integer landingForm;

    /**
     * 备注
     */
    @Size(max = 64, message = "[备注]编码长度不能超过64个字符")
    @ApiModelProperty("备注")
    private String remark;
}
