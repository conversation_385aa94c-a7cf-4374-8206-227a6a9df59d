package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户对象 sys_user
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "用户对象")
public class SysUserVO implements Serializable {

    private static final long serialVersionUID = -7697594416583743010L;
    @ApiModelProperty(value = "用户序号")
    private Long userId;

    @ApiModelProperty(value = "登录名称")
    private String userName;

    @ApiModelProperty(value = "用户名称")
    private String nickName;

    @ApiModelProperty(value = "手机号码")
    private String phonenumber;

    @ApiModelProperty(value = "用户性别")
    private String sex;

    @ApiModelProperty(value = "用户头像")
    private String avatar;
}
