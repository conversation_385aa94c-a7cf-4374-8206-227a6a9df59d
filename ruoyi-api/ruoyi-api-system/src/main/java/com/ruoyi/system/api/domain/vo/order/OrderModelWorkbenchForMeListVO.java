package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/3 10:56
 */
@Data
public class OrderModelWorkbenchForMeListVO implements Serializable {
    private static final long serialVersionUID = -8454520347735295564L;
    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long id;

    /**
     * 预选模特ID
     */
    @ApiModelProperty(value = "预选模特ID")
    private Long preselectModelId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 视频风格
     */
    @ApiModelProperty(value = "视频风格")
    private Integer videoStyle;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    @JsonIgnore
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    @JsonIgnore
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @JsonIgnore
    private Date addTime;

    /**
     * 意向模特选择超时时间
     */
    @ApiModelProperty(value = "意向模特选择超时时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date selectTimeout;
}
