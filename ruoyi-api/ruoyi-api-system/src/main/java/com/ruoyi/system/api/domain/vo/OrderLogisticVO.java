package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单物流信息返回对象VO
 *
 * <AUTHOR>
 * @date 2024-05-17
 */
@ApiModel(value = "订单物流信息返回对象VO")
@Data
public class OrderLogisticVO implements Serializable {
    private static final long serialVersionUID = -8693693193159202662L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 是否补发（0:补发,1:不是补发）
     */
    @ApiModelProperty(value = "是否补发（0:补发,1:不是补发）", notes = "0:补发,1:不是补发")
    private String reissue;

    /**
     * 发货时间（商家/运营 点击发货时间）
     */
    @ApiModelProperty(value = "发货时间（商家/运营 点击发货时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shippingTime;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    private String number;

    /**
     * 物流公司代码
     */
    @ApiModelProperty(value = "物流公司代码")
    private String carrier;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 物流信息详情
     */
    @ApiModelProperty(value = "物流信息详情")
    private List<LogisticInfoVO> logisticInfo;
}
