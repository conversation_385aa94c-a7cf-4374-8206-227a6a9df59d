package com.ruoyi.system.api.domain.vo;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.management.ValueExp;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 订单物流信息返回对象VO
 *
 * <AUTHOR>
 * @date 2024-10-15
 */
@Data
public class OrderLogisticSimpleVO implements Serializable {
    private static final long serialVersionUID = 2905639798813792449L;

    /**
     * 发货信息
     */
    @ApiModelProperty("发货信息")
    private ShippingInfoSimpleVO shippingInfoSimpleVO;

    /**
     * 视频id
     */
    @ApiModelProperty(value = "视频id")
    private Long videoId;

    /**
     * 收件地址ID (FK:order_video_model_shipping_address.id)
     */
    @ApiModelProperty(value = "收件地址ID")
    @JsonIgnore
    private Long shippingAddressId;

    /**
     * 发货时间（商家/运营 点击发货时间）
     */
    @ApiModelProperty(value = "发货时间（商家/运营 点击发货时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date shippingTime;

    /**
     * 物流主状态
     */
    @ApiModelProperty(value = "物流主状态")
    private String mainStatus;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    private String number;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Excel(name = "备注")
    private String remark;

    /**
     * 是否补发（0:补发,1:不是补发）
     */
    @ApiModelProperty(value = "是否补发（0:补发,1:不是补发）")
    private Integer reissue;

    /**
     * 补发原因
     */
    @ApiModelProperty(value = "补发原因")
    private String reissueCause;


    @ApiModelProperty(value = "是否确认收货：1:已收货,0:未收货")
    private Integer receipt;
    @ApiModelProperty(value = "收货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date receiptTime;

    /**
     * 标记物流状态（1:标记发货）
     */
    @ApiModelProperty(value = "标记物流状态（1:标记发货）")
    private Integer logisticFlag;

    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货原因")
    private String logisticFlagRemark;

    /**
     * 标记发货原因
     */
    @ApiModelProperty(value = "标记发货时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date logisticFlagTime;

    /**
     * 物流信息详情
     */
    @ApiModelProperty(value = "物流信息详情")
    private List<LogisticInfoVO> logisticInfo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "主键id")
    private Long id;
}
