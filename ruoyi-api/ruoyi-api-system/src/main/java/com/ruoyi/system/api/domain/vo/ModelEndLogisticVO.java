package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 模特端物流信息返回对象VO
 *
 * <AUTHOR>
 * @date 2025-02-28
 */
@ApiModel(value = "物流信息返回对象VO")
@Data
public class ModelEndLogisticVO implements Serializable {
    private static final long serialVersionUID = -1928746462828404265L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    private String number;

    /**
     * 运输商名称
     */
    @ApiModelProperty(value = "运输商名称")
    private String carrier;

    /**
     * 运输商联系电话
     */
    @ApiModelProperty(value = "运输商联系电话")
    private String carrierTel;

    /**
     * 运输商所属国家
     */
    @ApiModelProperty(value = "运输商所属国家")
    private String carrierCountry;

    /**
     * 签收时间
     */
    @ApiModelProperty(value = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+0")
    @Excel(name = "签收时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;


    /**
     * 发件地址_国家或地区（大写）
     */
    @ApiModelProperty(value = "发件地址_国家或地区（大写）")
    @Excel(name = "发件地址_国家或地区（大写）")
    private String saCountry;

    /**
     * 发件地址_州、省
     */
    @ApiModelProperty(value = "发件地址_州、省")
    @Excel(name = "发件地址_州、省")
    private String saState;

    /**
     * 发件地址_城市
     */
    @ApiModelProperty(value = "发件地址_城市")
    @Excel(name = "发件地址_城市")
    private String saCity;

    /**
     * 发件地址_街道
     */
    @ApiModelProperty(value = "发件地址_街道")
    @Excel(name = "发件地址_街道")
    private String saStreet;

    /**
     * 发件地址_邮编
     */
    @ApiModelProperty(value = "发件地址_邮编")
    @Excel(name = "发件地址_邮编")
    private String saPostalCode;

    /**
     * 发件地址_经度
     */
    @ApiModelProperty(value = "发件地址_经度")
    @Excel(name = "发件地址_经度")
    private String saLongitude;

    /**
     * 发件地址_纬度
     */
    @ApiModelProperty(value = "发件地址_纬度")
    @Excel(name = "发件地址_纬度")
    private String saLatitude;

    /**
     * 收件地址_国家或地区（大写）
     */
    @ApiModelProperty(value = "收件地址_国家或地区（大写）")
    @Excel(name = "收件地址_国家或地区（大写）")
    private String raCountry;

    /**
     * 收件地址_州、省
     */
    @ApiModelProperty(value = "收件地址_州、省")
    @Excel(name = "收件地址_州、省")
    private String raState;

    /**
     * 收件地址_城市
     */
    @ApiModelProperty(value = "收件地址_城市")
    @Excel(name = "收件地址_城市")
    private String raCity;

    /**
     * 收件地址_街道
     */
    @ApiModelProperty(value = "收件地址_街道")
    @Excel(name = "收件地址_街道")
    private String raStreet;

    /**
     * 收件地址_邮编
     */
    @ApiModelProperty(value = "收件地址_邮编")
    @Excel(name = "收件地址_邮编")
    private String raPostalCode;

    /**
     * 收件地址_经度
     */
    @ApiModelProperty(value = "收件地址_经度")
    @Excel(name = "收件地址_经度")
    private String raLongitude;

    /**
     * 收件地址_纬度
     */
    @ApiModelProperty(value = "收件地址_纬度")
    @Excel(name = "收件地址_纬度")
    private String raLatitude;

    /**
     * 物流信息详情
     */
    @ApiModelProperty(value = "物流信息详情")
    private List<ModelEndLogisticInfoVO> logisticInfo;
}
