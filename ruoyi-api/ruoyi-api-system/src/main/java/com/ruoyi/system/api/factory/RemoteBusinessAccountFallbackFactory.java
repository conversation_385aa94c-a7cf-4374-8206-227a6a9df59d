package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.constant.BusinessConstants;
import com.ruoyi.system.api.RemoteBusinessAccountService;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.FlowMemberDto;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BatchBusinessMemberValidityFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.ruoyi.system.api.domain.vo.SmsVo;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.order.WorkbenchVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;


/**
 * 商家服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteBusinessAccountFallbackFactory implements FallbackFactory<RemoteBusinessAccountService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteBusinessAccountFallbackFactory.class);

    @Override
    public RemoteBusinessAccountService create(Throwable cause) {
        log.error("商家服务调用失败:{}", cause.getMessage());
        return new RemoteBusinessAccountService() {
            @Override
            public BusinessAccountVO getAccountInfo(String merchantName, String source) {
                log.error("通过用户名查询商家信息失败:{}", merchantName);
                return null;
            }

            @Override
            public String refreshToken(String source) {
                log.error("刷新token失败");
                return null;
            }

            @Override
            public String deleteBusiness(Long id, String source) {
                log.error("删除商家账号失败:{}", id);
                return null;
            }

            @Override
            public BusinessAccountVO getBusinessAccountOne(BusinessAccountDTO dto, String source) {
                log.error("询商家信息失败:{}", dto);
                return null;
            }

            @Override
            public BusinessMemberActivity getBusinessMemberActivity(Integer packageType, String source) {
                log.error("获取会员活动失败:{}", packageType);
                return null;
            }

            @Override
            public BusinessVO getBusinessVo(BusinessDTO dto, String source) {
                log.error("通过账号id查询商家信息失败:{}", dto);
                return null;
            }

            @Override
            public WorkbenchVO getFinanceWorkbenchVo(String source) {
                log.error("获取工作台-财务部 提现待审批、分销待结算失败:{}");
                return WorkbenchVO.builder().payoutUnAuditCount(0L).distributionUnSettleCount(0L).build();
            }

            @Override
            public List<BusinessAccountDetailVO> getBusinessAccountDetailVOs(BusinessAccountDetailDTO dto) {
                log.error("获取商家列表数据失败:{}", dto);
                return List.of();
            }

            @Override
            public List<BusinessAccountDetailVO> queryUserInfo(BusinessAccountDetailDTO dto) {
                log.error("获取用户信息列表数据失败:{}", dto);
                return List.of();
            }

            @Override
            public List<BizUserDetailVO> getBizUserDetailList(BizUserDetailListDTO dto, String source) {
                log.error("获取登录账号列表数据失败:{}", dto);
                return List.of();
            }

            @Override
            public List<BizUserDetailVO> getUserChannel(BizUserDetailListDTO dto, String inner) {
                log.error("获取用户渠道列表数据失败:{}", dto);
                return List.of();
            }

            @Override
            public List<BusinessBalancePrepayVO> innerBusinessBalancePrepayList(BusinessBalancePrepayListDTO dto, String source) {
                log.error("获取预付款列表数据失败:{}", dto);
                return List.of();
            }

            @Override
            public BusinessBalancePrepayStatisticsVO getInnerStatistics(String source) {
                log.error("获取预付款统计失败:{}");
                return new BusinessBalancePrepayStatisticsVO();
            }

            @Override
            public String innerBusinessBalancePrepayUpdateAppId(PrepayUpdateAppIdDTO dto, String source) {
                log.error("修改钱包充值appid失败:{}");
                return null;
            }

            @Override
            public BusinessBalancePrepay innerUpdateOrderPayStatus(PrepayUpdatePayStatusDTO dto, String source) {
                log.error("修改钱包充值状态失败:{}");
                return null;
            }

            @Override
            public String loginForUnionId(String account, String passwd, String source) {
                log.error("商家登录失败:{}", account);
                return BusinessConstants.remoteExceptionPrefix + cause.getLocalizedMessage();
            }

            @Override
            public String updateBusinessBalance(BusinessBalanceDTO dto, String source) {
                log.error("商家修改余额:{}", dto);
                return null;
            }

            @Override
            public String saveBusinessMemberValidityFlow(BatchBusinessMemberValidityFlowDTO dto, String source) {
                log.error("保存会员有效期流水失败:{}", dto);
                return null;
            }

            @Override
            public BusinessAccountVO flowMember(FlowMemberDto dto) {
                log.error("会员状态流转:{}", dto);
                return null;
            }

            @Override
            public List<BusinessBalanceAuditFlow> queryValidBalanceAuditFlowList(BusinessBalanceAuditFlowValidListDTO dto, String source) {
                log.error("有效余额提现审核数据:{}", dto);
                return Collections.emptyList();
            }

            @Override
            public List<BusinessBalanceDetailLockInfoVO> queryValidLockList(BusinessBalanceDetailLockInfoDTO dto, String source) {
                log.error("获取有效锁定数据数据:{}", dto);
                return Collections.emptyList();
            }

            @Override
            public QrCodeDTO generateQrcode(Integer type, String code, String source) {
                log.error("获取二维码失败:{}", type);
                return null;
            }

            @Override
            public PhoneLoginVO phoneLogin(PhoneLoginDTO dto, String source) {
                log.error("手机号登录失败:{}", dto);
                return PhoneLoginVO.builder().msg(cause.getLocalizedMessage()).build();
            }

            @Override
            public ChannelPhoneLoginVO channelPhoneLogin(ChannelPhoneLoginDTO dto, String source) {
                log.error("手机号（渠道端）登录失败:{}", dto);
                return ChannelPhoneLoginVO.builder().msg(cause.getLocalizedMessage()).build();
            }

            @Override
            public String sendSms(SmsVo smsVo, String source) {
                //发送报错
                return cause.getLocalizedMessage();
            }

            @Override
            public String registerSendCode(SmsVo smsVo, String source) {
                return cause.getLocalizedMessage();
            }

            @Override
            public List<WithdrawDepositRecordVO> withdrawDepositRecord(WithdrawDepositRecordDTO dto, String source) {
                return Collections.emptyList();
            }

            @Override
            public Boolean updateRecentOrderTime(Long businessId, String source) {
                return Boolean.FALSE;
            }

            @Override
            public Boolean updatePaySucceed(Long businessId, String source) {
                return Boolean.FALSE;
            }

            @Override
            public BusinessBalancePrepay innerOnlineDetail(String prepayNum, String source) {
                log.error("获取充值详情:{}", prepayNum);
                return null;
            }

            @Override
            public String innerSubmitCredential(OnlineRechargeSubmitCredentialDTO dto, String source) {
                log.error("提交凭证失败:{}", dto);
                return null;
            }

            @Override
            public String updateBatchFieldNullToNull(Collection<String> orderNums, String source) {
                log.error("重置数据失败:{}", orderNums);
                return null;
            }

            @Override
            public List<Business> selectBusinessList(String source) {
                return null;
            }

            @Override
            public List<BusinessAccountDetailVO> getUserMemberStatusBySeedId(String source, Collection<String> seedId) {
                log.error("根据裂变种草id获取用户信息失败:{}", seedId);
                return List.of();
            }
        };
    }
}
