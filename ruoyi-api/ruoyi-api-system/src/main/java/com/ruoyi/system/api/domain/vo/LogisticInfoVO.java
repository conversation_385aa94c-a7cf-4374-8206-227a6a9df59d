package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-8-14
 */
@Data
public class LogisticInfoVO implements Serializable {
    private static final long serialVersionUID = 8128897967194781800L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 物流单号
     */
    @ApiModelProperty(value = "物流单号")
    private String number;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    private String description;

    /**
     * 物流主状态
     */
    @ApiModelProperty(value = "物流主状态")
    private String mainStatus;

    /**
     * 物流主状态简述
     */
    @ApiModelProperty(value = "物流主状态简述")
    private String mainStatusSketch;

    /**
     * 物流子状态
     */
    @ApiModelProperty(value = "物流子状态")
    private String subStatus;

    /**
     * 物流子状态简述
     */
    @ApiModelProperty(value = "物流子状态简述")
    private String subStatusSketch;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date curTime;

    /**
     * 当前节点在国家地区
     */
    @ApiModelProperty(value = "当前节点在国家地区")
    private String country;

    /**
     * 当前节点所在州、省
     */
    @ApiModelProperty(value = "当前节点所在州、省")
    private String state;

    /**
     * 当前节点所在城市
     */
    @ApiModelProperty(value = "当前节点所在城市")
    private String city;

    /**
     * 当前节点所在街道
     */
    @ApiModelProperty(value = "当前节点所在街道")
    private String street;

    /**
     * 当前节点所在经度
     */
    @ApiModelProperty(value = "当前节点所在经度")
    private String longitude;

    /**
     * 当前节点所在纬度
     */
    @ApiModelProperty(value = "当前节点所在纬度")
    private String latitude;

    /**
     * 当前所在地点
     */
    @ApiModelProperty(value = "当前所在地点")
    private String location;
}
