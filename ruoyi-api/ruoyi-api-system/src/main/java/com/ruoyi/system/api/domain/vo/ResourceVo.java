package com.ruoyi.system.api.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(value = "图片视频资源对象 resource")
@Data
public class ResourceVo implements Serializable
{

    private static final long serialVersionUID = -1930732169046071006L;
    /** 主键 */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    @Excel(name = "主键")
    private Long id;

    /** 资源名称 */
    @NotNull(message = "[资源名称]不能为空")
    @ApiModelProperty(value = "资源名称",required = true)
    @Excel(name = "资源名称")
    private String name;

    /** 视频url */
    @NotNull(message = "[视频url]不能为空")
    @ApiModelProperty(value = "视频url",required = true)
    @Excel(name = "视频url")
    private String videoUrl;

    /** 图片url */
    @NotNull(message = "[图片url]不能为空")
    @ApiModelProperty(value = "图片url", required = true)
    @Excel(name = "图片url")
    private String picUrl;

    @ApiModelProperty(value = "0-商家上传， 1-平台上传")
    private Integer uploadType;
}
