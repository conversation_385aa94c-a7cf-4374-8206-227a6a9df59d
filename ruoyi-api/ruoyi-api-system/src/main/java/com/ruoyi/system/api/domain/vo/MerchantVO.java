package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 商家信息对象 merchant
 *
 * <AUTHOR>
 * @date 2024-05-28
 */
@ApiModel(value = "商家信息对象VO")
@Data
public class MerchantVO implements Serializable {

    private static final long serialVersionUID = -3783519283315154863L;
    /**
     * 商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private Long merchantId;

    /**
     * 商家账号
     */
    @ApiModelProperty(value = "商家账号")
    private String merchantName;

    /**
     * 商家编码
     */
    @ApiModelProperty(value = "商家编码")
    private String merchantCode;
}
