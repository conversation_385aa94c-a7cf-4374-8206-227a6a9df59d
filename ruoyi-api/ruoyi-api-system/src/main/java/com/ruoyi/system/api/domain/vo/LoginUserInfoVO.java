package com.ruoyi.system.api.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @program :woniu-world
 * @description :
 * @create :2024-09-06 10:18
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LoginUserInfoVO implements Serializable {
    private static final long serialVersionUID = -2091159569145964185L;

    @ApiModelProperty(value = "处理人ID")
    private Long userId;

    @ApiModelProperty(value = "用户类型：1:商家,2:运营,3:模特,9:系统")
    private Integer userType;

    @ApiModelProperty(value = "登录人名称")
    private String name;

    @ApiModelProperty(value = "登录人微信名称")
    private String nickName;

    @ApiModelProperty(value = "登录人微信手机号")
    private String phone;
}
