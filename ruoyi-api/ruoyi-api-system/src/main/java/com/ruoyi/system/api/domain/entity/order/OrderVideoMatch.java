package com.ruoyi.system.api.domain.entity.order;

import cn.hutool.core.date.DatePattern;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单_视频_预选模特对象 order_video_match
 *
 * <AUTHOR>
 * @date 2024-11-15
 */
@ApiModel(value = "订单_视频_预选模特对象 order_video_match")
@TableName("order_video_match")
@Data
public class OrderVideoMatch implements Serializable {

    private static final long serialVersionUID = -8376963500790185406L;
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 视频id
     */
    @NotNull(message = "[视频id]不能为空")
    @ApiModelProperty(value = "视频id", required = true)
    private Long videoId;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 产品图变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "产品图变更(1:变更了,0:未变更)")
    private Boolean productPicChange;

    /**
     * 商品信息变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "商品信息变更(1:变更了,0:未变更)")
    private Boolean goodsInfoChange;

    @ApiModelProperty(value = "照片数量变更(1:变更了,0:未变更)")
    private Boolean picCountChange;

    @ApiModelProperty(value = "意向模特变更(1:变更了,0:未变更)")
    private Boolean intentionModelChange;

    /**
     * 拍摄建议变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "拍摄建议变更(1:变更了,0:未变更)")
    private Boolean shootRequiredChange;

    /**
     * 注意事项变更(1:变更了,0:未变更)
     */
    @ApiModelProperty(value = "注意事项变更(1:变更了,0:未变更)")
    private Boolean cautionsChange;

    @ApiModelProperty(value = "产品卖点变更(1:变更了,0:未变更)")
    private Boolean sellingPointProductChange;

    @ApiModelProperty(value = "商品规格要求变更(1:变更了,0:未变更)")
    private Boolean orderSpecificationRequireChange;

    @ApiModelProperty(value = "特别强调变更(1:变更了,0:未变更)")
    private Boolean particularEmphasisChange;

    /**
     * 匹配次数
     */
    @ApiModelProperty(value = "匹配次数")
    private Integer count;

    /**
     * 状态（1:正常,2:暂停）
     */
    @ApiModelProperty(value = "状态（1:正常,2:暂停）")
    private Integer status;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 排单类型（1:排单,2:携带排单）
     */
    @ApiModelProperty(value = "排单类型（1:排单,2:携带排单）")
    private Integer scheduleType;

    /**
     * 模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）
     */
    @ApiModelProperty(value = "模特佣金单位（美金:USD,加币:CAD,英镑:GBP,欧元:EUR）")
    private String commissionUnit;

    /**
     * 模特佣金
     */
    @ApiModelProperty(value = "模特佣金")
    private BigDecimal commission;

    /**
     * 超额说明
     */
    @ApiModelProperty(value = "超额说明")
    private String overstatement;

    /**
     * 携带类型（1:主携带,2:被携带）
     */
    @ApiModelProperty(value = "携带类型（1:主携带,2:被携带）")
    private Integer carryType;

    /**
     * 主携带数量
     */
    @ApiModelProperty(value = "主携带数量")
    private Integer mainCarryCount;

    /**
     * 主携带视频订单id (FK:order_video.id)
     */
    @ApiModelProperty(value = "主携带视频订单id (FK:order_video.id)")
    private Long mainCarryVideoId;

    /**
     * 携带单忽略标记（1：表示携带该订单的主携带被回退后，且该订单状态为已完成时，会设置此值，表示该订单不计入携带数统计）
     */
    @ApiModelProperty(value = "携带单忽略标记")
    private Integer carryIgnore;

    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    private String shippingRemark;

    /**
     * 发货图片(关联资源id)
     */
    @ApiModelProperty(value = "发货图片(关联资源id)")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String shippingPic;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    /**
     * 拍摄模特id
     */
    @ApiModelProperty(value = "拍摄模特id")
    private Long shootModelId;

    /**
     * 拍摄模特类型(0:影响者,1:素人)
     */
    @ApiModelProperty(value = "拍摄模特类型(0:影响者,1:素人)")
    private Integer shootModelType;

    /**
     * 拍摄模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "拍摄模特平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private String shootModelPlatform;

    /**
     * 拍摄模特合作深度(0:一般模特,1:优质模特,2:中度模特)
     */
    @ApiModelProperty(value = "拍摄模特合作深度(0:一般模特,1:优质模特,2:中度模特)")
    private Integer shootModelCooperation;

    /**
     * 拍摄模特对接人ID
     */
    @ApiModelProperty(value = "拍摄模特对接人ID")
    private Long shootModelPersonId;

    /**
     * 拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）
     */
    @ApiModelProperty(value = "拍摄模特添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）")
    private Integer shootModelAddType;

    /**
     * 拍摄模特对接人名称
     */
    @ApiModelProperty(value = "拍摄模特对接人名称")
    private String shootModelPersonName;

    /**
     * 模特对接客服ID
     */
    @ApiModelProperty(value = "模特对接客服ID")
    private Long issueId;

    /**
     * 拍摄要求原文
     */
    @ApiModelProperty(value = "拍摄要求原文")
    private String shootRequiredOriginal;

    /**
     * 拍摄要求译文
     */
    @ApiModelProperty(value = "拍摄要求译文")
    private String shootRequiredTranslation;

    /**
     * 创建人姓名
     */
    @ApiModelProperty(value = "创建人姓名")
    private String createBy;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private Long createById;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新人姓名
     */
    @ApiModelProperty(value = "更新人姓名")
    private String updateBy;

    /**
     * 更新人ID
     */
    @ApiModelProperty(value = "更新人ID")
    private Long updateById;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 暂停匹配原因
     */
    @ApiModelProperty(value = "暂停匹配原因")
    private String pauseReason;


    /**
     * 拍摄模特注意事项
     */
    @ApiModelProperty(value = "拍摄模特注意事项")
    private String shootAttention;

    /**
     * 拍摄注意事项对象存储键值
     */
    @ApiModelProperty("拍摄注意事项对象存储键值")
    private String shootAttentionObjectKey;

}
