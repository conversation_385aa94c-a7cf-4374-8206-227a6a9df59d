package com.ruoyi.system.api.domain.dto.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Null;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/9/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderMemberChannelListDTO implements Serializable {

    private static final long serialVersionUID = -7791794683307140881L;
    /**
     * 套餐类型
     */
    @ApiModelProperty("套餐类型：0=季度会员,1=年度会员,2=三年会员,3=非该渠道邀请")
    private Integer memberPackageType;

    /**
     * 状态
     */
    @ApiModelProperty("状态（0-未结算，1-已结算）")
    private List<Integer> settleStatus;

    @ApiModelProperty("渠道类型：2-分销渠道 7-裂变")
    private Integer channelType;

    @ApiModelProperty("登录账号列表")
    private List<Long> bizUserIds;

    /**
     * 关键字
     */
    @ApiModelProperty("关键字")
    private String keyword;

    @ApiModelProperty("裂变渠道关键字")
    private String searchName;

    /**
     * 渠道ID（查询 渠道端H5会员邀请记录、分销渠道详情会员成交记录使用 条件为强等）
     */
    @ApiModelProperty("渠道ID")
    private String channelId;

    @ApiModelProperty("渠道ID列表")
    private List<Long> channelIds;

    @Null(message = "[businessIds]不允许传值")
    private Set<Long> businessIds;

    @ApiModelProperty("是否过滤结算金额为0的数据")
    private Integer isFiltrationZero;

    @ApiModelProperty("打款时间开始")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payoutTimeStart;

    @ApiModelProperty("打款时间结束")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date payoutTimeEnd;
}
