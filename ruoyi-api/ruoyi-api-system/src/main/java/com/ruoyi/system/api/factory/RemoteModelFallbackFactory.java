package com.ruoyi.system.api.factory;

import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.RemoteModelService;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.biz.model.CannotAcceptModelDTO;
import com.ruoyi.system.api.domain.entity.Model;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedOustModelCountInfo;
import com.ruoyi.system.api.domain.vo.biz.model.AddPreselectModelListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelChangeVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.ruoyi.system.api.model.LoginModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 模特服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteModelFallbackFactory implements FallbackFactory<RemoteModelService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteModelFallbackFactory.class);

    @Override
    public RemoteModelService create(Throwable throwable) {
        log.error("模特服务调用失败");
        return new RemoteModelService() {
            @Override
            public List<ModelInfoVO> innerList(ModelListDTO modelListDTO, String source) {
                log.error("查询模特信息列表失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<Model> queryCannotAcceptList(CannotAcceptModelDTO dto, String source) {
                log.error("查询不可接单的模特失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<ModelPerson> queryModelPerson(Collection<Long> modelIds, String source) {
                log.error("查询模特关联运营失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public LoginModel getModelByModelLoginAccount(String account) {
                log.error("根据模特账号查询模特数据失败:{}", throwable.getMessage());
                throw new ServiceException("根据模特账号查询模特数据失败:" + throwable.getLocalizedMessage());
            }

            @Override
            public String modelLoginForAccount(Long account) {
                log.error("更新模特最新登录时间失败:{}", throwable.getMessage());
                throw new ServiceException("更新模特最新登录时间失败:" + throwable.getLocalizedMessage());
            }

            @Override
            public List<ModelInfoVO> queryLikeModelList(ModelListDTO modelListDTO, String source) {
                log.error("模糊查询模特信息列表（模特名称、模特账号）失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<ModelOrderSimpleVO> queryModelSimpleList(ModelListDTO modelListDTO, String source) {
                log.error("查询模特简单信息（用于订单列表模特数据）失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<ModelChangeVO> selectModelChangeList(ModelListDTO modelListDTO, String source) {
                log.error("查询模特信息 用于记录视频订单模特变更记录 失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<AddPreselectModelListVO> selectModelInfoOfPreselection(ModelListDTO modelListDTO, String source) {
                log.error("查询模特信息（预选模特对象）失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<ModelPerson> selectModelPersonByUserIds(Collection<Long> userIds, String source) {
                log.error("查询当前运营关联的模特失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<CustomerServiceAddedOustModelCountInfo> getEnglishCustomerServiceAddedOustModelCounts(String date, String source) {
                return null;
            }

            @Override
            public List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceModelData(String source) {
                return null;
            }

            @Override
            public List<ModelInfoVO> getModelsForPreselectCleanup(String source) {
                log.error("查询需要清理的预选模特失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }
        };
    }
}
