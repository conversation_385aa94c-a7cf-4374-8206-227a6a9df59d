package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.core.domain.vo.UserVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-24
 */
@Data
public class WorkOrderTaskDetailListVO implements Serializable {
    private static final long serialVersionUID = -4753018400945345726L;

    /**
     * 工单详情主键 order_video_task_detail.id
     */
    @ApiModelProperty(value = "工单详情主键 order_video_task_detail.id")
    private Long id;

    /**
     * 任务单ID(FK:order_video_task.id)
     */
    @ApiModelProperty(value = "任务单ID(FK:order_video_task.id)")
    private Long taskId;

    /**
     * 工单编号
     */
    @ApiModelProperty(value = "工单编号")
    private String taskNum;

    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date submitTime;

    /**
     * 工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）
     */
    @ApiModelProperty(value = "工单类型（1：模特没收到，2：催素材，3：下架视频，4：需剪辑，5：其他，6：上传异常）")
    private Integer workOrderType;

    /**
     * 工单内容
     */
    @ApiModelProperty(value = "工单内容")
    private String content;

    /**
     * 补充剪辑要求
     */
    @ApiModelProperty(value = "补充剪辑要求")
    private String clipRecord;

    /**
     * 问题图片（FK:order_resource.id）
     */
    @JsonIgnore
    private String issuePicId;

    /**
     * 问题图片
     */
    @ApiModelProperty(value = "问题图片")
    private List<String> issuePic = new ArrayList<>();

    /**
     * 优先级（1:紧急,2:一般）
     */
    @ApiModelProperty(value = "优先级（1:紧急,2:一般）", notes = "1:紧急,2:一般")
    private Integer priority;

    /**
     * 提交人id
     */
    @JsonIgnore
    private Long submitById;

    /**
     * 提交人
     */
    @ApiModelProperty(value = "提交人")
    private UserVO submit;

    /**
     * 处理人id
     */
    @JsonIgnore
    private Long assigneeId;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private UserVO assignee;

    /**
     * 最新回复时间
     */
    @ApiModelProperty(value = "最新回复时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date lastReplyTime;

    /**
     * 状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）
     */
    @ApiModelProperty(value = "状态（1：待处理，2：处理中，3：申请取消中，4：已完成，5：已拒绝，6：已关闭）")
    private Integer status;

    @ApiModelProperty(value = "最新处理记录")
    private String isNewProcess;
}
