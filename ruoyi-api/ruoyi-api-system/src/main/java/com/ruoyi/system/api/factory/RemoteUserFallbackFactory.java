package com.ruoyi.system.api.factory;

import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.vo.SysUserVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ChineseCustomerServiceDataVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.EnglishCustomerServiceDataVO;
import com.ruoyi.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<RemoteUserService>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败");
        return new RemoteUserService() {
            @Override
            public LoginUser getUserInfo(String username, String source) {
                log.error("获取用户失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public Boolean registerUserInfo(SysUser sysUser, String source) {
                log.error("注册用户失败:{}", throwable.getMessage());
                return false;
            }

            @Override
            public List<SysUser> listNoPage(SysUserListDTO dto, String source) {
                log.error("获取用户列表失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public Boolean recordLoginInfo(SysUser sysUser, String source) {
                log.error("记录用户登录信息失败:{}", throwable.getMessage());
                return false;
            }

            @Override
            public SysUser selectUserById(Long userId, String source) {
                log.error("查询用户失败:{}", throwable.getMessage());
                return null;
            }

            @Override
            public List<SysUserVO> getUserLevel(String source) {
                log.error("获取当前用户以及他下级部门的用户信息失败:{}", throwable.getMessage());
                return Collections.emptyList();
            }

            @Override
            public List<ChineseCustomerServiceDataVO> selectChineseCustomerServiceData(String source) {
                return null;
            }

            @Override
            public List<EnglishCustomerServiceDataVO> selectEnglishCustomerServiceData(String source) {
                return null;
            }
        };
    }
}
