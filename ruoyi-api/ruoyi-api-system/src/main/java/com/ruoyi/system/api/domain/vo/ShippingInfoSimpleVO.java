package com.ruoyi.system.api.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/15
 */
@ApiModel(value = "发货信息")
@Data
public class ShippingInfoSimpleVO implements Serializable {
    private static final long serialVersionUID = -4893874138200376982L;

    /**
     * 主键
     */
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 回退ID (FK:order_video_rollback_record.id)
     */
    @ApiModelProperty("回退ID")
    private Long rollbackId;

    /**
     * 模特
     */
    @ApiModelProperty("模特")
    private ModelInfoVO shootModel;

    /**
     * 收件人
     */
    @ApiModelProperty(value = "收件人")
    private String recipient;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 州
     */
    @ApiModelProperty(value = "州")
    private String state;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipcode;

    /**
     * 国家"（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）"
     */
    @ApiModelProperty(value = "国家（1:英国,2:加拿大,3:德国,4:法国,5:意大利,6:西班牙,7:美国）")
    private Integer nation;

    /**
     * 手机号
     */
    @ApiModelProperty("手机号")
    private String phone;

    /**
     * 手机号是否可见(0-不可见,1-可见)
     */
    @ApiModelProperty(value = "手机号是否可见(0-不可见,1-可见)")
    private Integer phoneVisible;

    /**
     * 发货备注
     */
    @ApiModelProperty(value = "发货备注")
    private String shippingRemark;

    /**
     * 发货图片(关联资源id)
     */
    @ApiModelProperty(value = "发货图片(关联资源id)")
    @JsonIgnore
    private String shippingPic;

    /**
     * 发货图片
     */
    @ApiModelProperty(value = "发货图片")
    private List<String> shippingPics = new ArrayList<>();

    /**
     * 是否补发（0:补发,1:不是补发）
     */
    @ApiModelProperty(value = "是否补发（0:补发,1:不是补发）", notes = "0:补发,1:不是补发")
    private Integer reissue;
}
