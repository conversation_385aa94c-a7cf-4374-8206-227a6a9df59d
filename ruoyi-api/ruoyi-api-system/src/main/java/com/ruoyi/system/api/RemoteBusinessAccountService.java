package com.ruoyi.system.api;

import com.ruoyi.common.core.constant.SecurityConstants;
import com.ruoyi.common.core.constant.ServiceNameConstants;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.BusinessBalanceDTO;
import com.ruoyi.system.api.domain.dto.FlowMemberDto;
import com.ruoyi.system.api.domain.dto.QrCodeDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BatchBusinessMemberValidityFlowDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessAccountDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.ChannelPhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.PhoneLoginDTO;
import com.ruoyi.system.api.domain.dto.biz.business.account.user.BizUserDetailListDTO;
import com.ruoyi.system.api.domain.dto.biz.business.balance.*;
import com.ruoyi.system.api.domain.dto.order.WithdrawDepositRecordDTO;
import com.ruoyi.system.api.domain.entity.biz.business.Business;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalanceAuditFlow;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessBalancePrepay;
import com.ruoyi.system.api.domain.entity.biz.business.BusinessMemberActivity;
import com.ruoyi.system.api.domain.vo.SmsVo;
import com.ruoyi.system.api.domain.vo.biz.business.*;
import com.ruoyi.system.api.domain.vo.biz.business.balance.BusinessBalanceDetailLockInfoVO;
import com.ruoyi.system.api.domain.vo.biz.business.balance.WithdrawDepositRecordVO;
import com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO;
import com.ruoyi.system.api.domain.vo.order.WorkbenchVO;
import com.ruoyi.system.api.factory.RemoteBusinessAccountFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;


/**
 * 商家服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteBusinessAccountService", value = ServiceNameConstants.BIZ_SERVICE,
        fallbackFactory = RemoteBusinessAccountFallbackFactory.class
)
public interface RemoteBusinessAccountService {
    /**
     * 通过unionId查询商家信息
     *
     * @param unionId unionId
     * @param source  请求来源
     * @return 结果
     */
    @GetMapping("/account/info/{unionId}")
    public BusinessAccountVO getAccountInfo(@PathVariable("unionId") String unionId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 刷新token
     * @param source
     * @return
     */
    @PostMapping("/account/refreshToken")
    public String refreshToken(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 删除商家
     * @param id
     * @param source
     * @return
     */
    @DeleteMapping("/business/deleteBusiness/{id}")
    public String deleteBusiness(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取商家信息
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/getBusinessAccountOne")
    public BusinessAccountVO getBusinessAccountOne(@RequestBody BusinessAccountDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 获取活动
     * @param packageType
     * @param source
     * @return
     */
    @GetMapping("/business/getBusinessMemberActivity")
    public BusinessMemberActivity getBusinessMemberActivity(@RequestParam("packageType") Integer packageType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 获取商家信息
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/getBusinessVo")
    public BusinessVO getBusinessVo(@RequestBody BusinessDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取工作台-财务部 提现待审批、分销待结算
     * @param source
     * @return
     */
    @PostMapping("/business/getFinanceWorkbenchVo")
    public WorkbenchVO getFinanceWorkbenchVo(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取账号列表数据
     * @param dto
     * @return
     */
    @PostMapping("/business/getBusinessAccountDetailVOs")
    public List<BusinessAccountDetailVO> getBusinessAccountDetailVOs(@RequestBody BusinessAccountDetailDTO dto);


    /**
     * 查询用户信息
     * @param dto
     * @return
     */
    @PostMapping("/business/getBizUserInfo")
    public List<BusinessAccountDetailVO> queryUserInfo(@RequestBody BusinessAccountDetailDTO dto);


    /**
     * 获取登录账号详情列表
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/getBizUserDetailList")
    public List<BizUserDetailVO> getBizUserDetailList(@RequestBody BizUserDetailListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取预付款审核表列表*
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/inner/businessBalancePrepay/list")
    public List<BusinessBalancePrepayVO> innerBusinessBalancePrepayList(@RequestBody BusinessBalancePrepayListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * * 获取预付款审核统计
     * @param source
     * @return
     */
    @GetMapping("/business/inner/businessBalancePrepay/statistics")
    public BusinessBalancePrepayStatisticsVO getInnerStatistics(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改钱包充值appid
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/inner/businessBalancePrepay/updateAppId")
    public String innerBusinessBalancePrepayUpdateAppId(@RequestBody PrepayUpdateAppIdDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改钱包充值状态
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/inner/businessBalancePrepay/updateOrderPayStatus")
    public BusinessBalancePrepay innerUpdateOrderPayStatus(@RequestBody PrepayUpdatePayStatusDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/account/login")
    public String loginForUnionId(@RequestParam("account") String account, @RequestParam("passwd") String passwd, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改商家余额接口
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/backend/updateBusinessBalance")
    public String updateBusinessBalance(@RequestBody BusinessBalanceDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 保存会员有效期流水
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/saveBusinessMemberValidityFlow")
    public String saveBusinessMemberValidityFlow(@RequestBody BatchBusinessMemberValidityFlowDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 会员状态流转
     * @param dto
     * @return
     */
    @PostMapping("/business/flowMember")
    public BusinessAccountVO flowMember(@RequestBody FlowMemberDto dto);

    /**
     * 有效余额提现审核数据
     * * @param dto
     * @return
     */
    @PostMapping("/business/queryValidBalanceAuditFlowList")
    public List<BusinessBalanceAuditFlow> queryValidBalanceAuditFlowList(@RequestBody BusinessBalanceAuditFlowValidListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * * 获取有效锁定数据
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/queryValidLockList")
    List<BusinessBalanceDetailLockInfoVO> queryValidLockList(@RequestBody BusinessBalanceDetailLockInfoDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    /**
     * 生成二维码
     * @param type      二维码类型
     * @param code      专属链接
     * @param source
     * @return
     */
    @PostMapping("/account/generateQrcode")
    public QrCodeDTO generateQrcode(@RequestParam("type") Integer type, @RequestParam("code") String code, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 手机号登录
     * @param dto
     * @return
     */
    @PostMapping("/account/phoneLogin")
    public PhoneLoginVO phoneLogin(@RequestBody PhoneLoginDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 手机号登录（渠道）
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/account/channelPhoneLogin")
    public ChannelPhoneLoginVO channelPhoneLogin(@RequestBody ChannelPhoneLoginDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 发送短信*
     * @param smsVo
     * @param source
     * @return
     */
    @PostMapping("/security/sms")
    public String sendSms(@RequestBody SmsVo smsVo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册发送短信
     *
     * @param smsVo
     * @param source
     * @return
     */
    @PostMapping("/security/registerSendCode")
    public String registerSendCode(@RequestBody SmsVo smsVo, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取用户渠道
     * @param dto
     * @param inner
     * @return
     */
    @PostMapping("/business/getUserChannel")
    List<BizUserDetailVO> getUserChannel(@RequestBody BizUserDetailListDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String inner);

    /**
     * 获取视频订单提现记录
     */
    @PostMapping("/business/withdraw-deposit-record")
    List<WithdrawDepositRecordVO> withdrawDepositRecord(@RequestBody WithdrawDepositRecordDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 商家排单时 更新最近排单时间 以及删除未排单事件
     */
    @PostMapping("/business/update-recent-order-time")
    Boolean updateRecentOrderTime(@RequestParam(value = "businessId") Long businessId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 商家提交凭证、微信、支付宝支付成功
     */
    @PostMapping("/business/update-pay-succeed")
    Boolean updatePaySucceed(@RequestParam(value = "businessId") Long businessId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取获取充值详情
     * @param prepayNum
     * @param source
     * @return
     */
    @GetMapping("/business/inner/businessBalancePrepay/online/{prepayNum}")
    BusinessBalancePrepay innerOnlineDetail(@PathVariable("prepayNum") String prepayNum, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 提交凭证
     * @param dto
     * @param source
     * @return
     */
    @PostMapping("/business/inner/businessBalancePrepay/online/submit-credential")
    String innerSubmitCredential(@RequestBody OnlineRechargeSubmitCredentialDTO dto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改
     * @param orderNums
     * @param source
     * @return
     */
    @PostMapping("/business/inner/businessBalancePrepay/online/updateBatchFieldNullToNull")
    String updateBatchFieldNullToNull(@RequestBody Collection<String> orderNums, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 查询商家信息
     */
    @GetMapping("/business/inner/select-business-list")
    List<Business> selectBusinessList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据裂变种草id获取用户信息
     */
    @PostMapping("/business/inner/userMemberStatusBySeedId")
    List<BusinessAccountDetailVO> getUserMemberStatusBySeedId(@RequestHeader(SecurityConstants.FROM_SOURCE) String source, Collection<String> seedId);
}
