package com.ruoyi.system.api.domain.vo.order;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单_视频_模特选择记录对象
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
public class OrderVideoModelSelectListVO implements Serializable {
    private static final long serialVersionUID = 7330373463231496216L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 订单超时标记
     */
    @JsonIgnore
    private Integer flag;

    /**
     * 视频订单id
     */
    @ApiModelProperty(value = "视频订单id")
    private Long videoId;

    /**
     * 产品图URI
     */
    @ApiModelProperty(value = "产品图URI")
    private String productPic;

    /**
     * 产品英文名
     */
    @ApiModelProperty(value = "产品英文名")
    private String productEnglish;

    /**
     * 视频编码
     */
    @ApiModelProperty(value = "视频编码")
    private String videoCode;

    /**
     * 视频风格
     */
    @ApiModelProperty(value = "视频风格")
    private Integer videoStyle;

    /**
     * 视频时长
     */
    @ApiModelProperty(value = "视频时长")
    private Integer videoDuration;

    /**
     * 平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)
     */
    @ApiModelProperty(value = "平台(0:Amazon,1:tiktok,2:其他,3:APP/解说类)")
    private Integer platform;

    /**
     * 照片数量（1:2张/$10,2:5张/$20）
     */
    @ApiModelProperty(value = "照片数量（1:2张/$10,2:5张/$20）")
    @JsonIgnore
    private Integer picCount;

    /**
     * 退款照片数量
     */
    @ApiModelProperty(value = "退款照片数量")
    @JsonIgnore
    private Integer refundPicCount;

    /**
     * 剩余退款照片数量
     */
    @ApiModelProperty(value = "剩余退款照片数量")
    private Integer surplusPicCount;

    /**
     * 模特id
     */
    @ApiModelProperty(value = "模特id")
    private Long modelId;

    /**
     * 当前进度状态（1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄,6:您已取消申请）
     */
    @ApiModelProperty(value = "当前进度状态（1:正在审核中,2:过期未确认,3:卖方取消,4:您已拒绝,5:已确认拍摄,6:您已取消申请）")
    private Integer selectStatus;

    /**
     * 选择时间
     */
    @ApiModelProperty(value = "选择时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date selectTime;

    /**
     * 意向模特选择超时时间
     */
    @ApiModelProperty(value = "意向模特选择超时时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN, timezone = "UTC+0")
    private Date selectTimeout;

    /**
     * 添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）
     */
    @ApiModelProperty(value = "添加方式（1:意向模特,2:模特自选,3:运营添加,4:客服分发）")
    @JsonIgnore
    private Integer addType;

    /**
     * 添加时间
     */
    @ApiModelProperty(value = "添加时间")
    @JsonIgnore
    private Date addTime;

    /**
     * 能否撤销
     */
    @ApiModelProperty(value = "能否撤销")
    private boolean canCancel;
}
