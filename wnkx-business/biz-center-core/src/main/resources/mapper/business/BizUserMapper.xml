<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BizUserMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BizUser">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
            <result property="pic" column="pic" jdbcType="VARCHAR"/>
            <result property="unionid" column="unionid" jdbcType="VARCHAR"/>
            <result property="phone" column="phone" jdbcType="VARCHAR"/>
            <result property="externalUserId" column="external_user_id" jdbcType="VARCHAR"/>
            <result property="status" column="STATUS" jdbcType="BOOLEAN"/>
            <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDel" column="is_del" jdbcType="BOOLEAN"/>
    </resultMap>

    <select id="bizUserList" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.BizUserListVO">
        select bu.id,bu.name,bu.nick_name,b.name businessName,b.id businessId,bu.pic,bu.unionid,bu.phone,bu.is_proxy,bu.customer_type,bu.waiter_id
        ,bu.status, bu.last_login_time, buc.register_time as create_time, bu.update_time, bu.account_type, buc.register_channel_type channelType,
        bu.connect_user_name, buc.register_channel_id channelId,
        LENGTH(TRIM(bu.unionid)) > 0 AS unionIdStatus
        from biz_user bu
        left join business_account ba on bu.id = ba.biz_user_id
        left join business b on b.id = ba.business_id
        left join biz_user_channel buc on bu.id = buc.biz_user_id
        <where>
            <if test="searchName != null and searchName != ''"> and (
                bu.id = #{searchName}
                or bu.name like concat('%', #{searchName}, '%')
                or bu.nick_name like concat('%', #{searchName}, '%')
                or b.name like concat('%', #{searchName}, '%')
                or bu.phone like concat('%', #{searchName}, '%')
                <if test="waiterIds != null and waiterIds.size() != 0">
                    or bu.waiter_id in
                    <foreach collection="waiterIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                )</if>
            <if test="bizUserId != null">
                and bu.id = #{bizUserId}
            </if>
            <if test="accountType != null">
                and bu.account_type = #{accountType}
            </if>
            <if test="channelType != null">
                and buc.register_channel_type = #{channelType}
            </if>
            <if test="status != null">
                and bu.status = #{status}
            </if>
        </where>
    </select>

    <select id="channelBizUserId" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.BizUserChannelListVO">
        select bu.id,bu.name,bu.nick_name,b.name businessName,b.member_code,b.member_status,b.member_validity,bu.pic,bu.unionid,bu.phone,bu.is_proxy,bu.customer_type,bu.waiter_id
        ,bu.status, bu.last_login_time, bu.create_time, bu.update_time, bu.account_type, bu.unbind_time,dc.id channelId,
        bu.connect_user_name
        from distribution_channel dc
        inner join biz_user bu on bu.id = dc.biz_user_id
        left join business_account ba on bu.id = ba.biz_user_id
        left join business b on b.id = ba.business_id
        where dc.channel_type = ${@<EMAIL>}
        <if test="keyword != null and keyword != ''">
            and (
            bu.name like concat('%', #{keyword}, '%')
            or bu.nick_name like concat('%', #{keyword}, '%')
            or b.name like concat('%', #{keyword}, '%')
            or b.member_code like concat('%', #{keyword}, '%')
            )
        </if>
        <if test="bizUserIds != null and bizUserIds.size() > 0">
            and bu.id IN
            <foreach collection="bizUserIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="channelIds != null and channelIds.size() > 0">
        and dc.id IN
            <foreach collection="channelIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="bizUserDetailList" resultType="com.ruoyi.system.api.domain.vo.biz.business.user.BizUserDetailVO">
        select bu.*,
               ba.account,
               ba.name as accountName,
               b.owner_account,
               b.id businessId,
               b.name accountBusinessName,
               b.member_code,
               bu.connect_user_name
        from biz_user bu
        left join business_account ba on bu.id = ba.biz_user_id
        left join business b on b.id = ba.business_id
        <where>
            <if test="ids != null and ids.size() != 0">
                and bu.id in
                <foreach collection="ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getBizUserByBusinessId" resultType="com.ruoyi.system.api.domain.entity.biz.business.BizUser">
        select
            bu.*
        from business_account ba
        inner join biz_user bu on ba.biz_user_id = bu.id
        where ba.business_id = #{businessId}
    </select>
    <select id="getBizUserIdByMemberStatus" resultType="java.lang.Long">
        select
        bu.id
        from biz_user bu
        left join business_account ba on bu.id = ba.biz_user_id
        left join business b on b.id = ba.business_id
        <where>
            <if test="memberStatus != null and memberStatus >= 1">
                and b.member_status = #{memberStatus}
            </if>
            <if test="memberStatus != null and memberStatus == 0">
                ( b.member_status = #{memberStatus}
                or bu.account_type = ${@<EMAIL>}
                )
            </if>
        </where>
    </select>

    <sql id="Base_Column_List">
        id,nick_name,pic,
        unionid,phone,external_user_id,
        STATUS,last_login_time,create_time,
        update_time,is_del
    </sql>

    <select id="queryUserInfo"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO">
        SELECT
        <if test="dto.account == null and dto.account == '' and dto.memberCode == null and dto.memberCode == '' or dto.nickName != null">
            bu.id AS biz_user_id
        </if>
        <if test="dto.account != null and dto.account != ''">
            ba.biz_user_id AS biz_user_id
        </if>
        <if test="dto.memberCode != null and dto.memberCode != ''">
            ba.biz_user_id AS biz_user_id
        </if>
        FROM
        biz_user bu
        <if test="dto.account != null and dto.account != ''">
            JOIN business_account ba ON bu.id = ba.biz_user_id and ba.account = #{dto.account}
        </if>
        <if test="dto.memberCode != null and dto.memberCode != ''">
            JOIN business_account ba ON bu.id = ba.biz_user_id
            JOIN business b ON ba.business_id = b.id and b.member_code = #{dto.memberCode}
        </if>
        <where>
            <if test="dto.nickName != null and dto.nickName != ''">
                bu.nick_name LIKE CONCAT('%', #{dto.nickName}, '%')
            </if>
        </where>
    </select>
</mapper>
