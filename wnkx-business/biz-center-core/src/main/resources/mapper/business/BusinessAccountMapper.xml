<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.business.mapper.BusinessAccountMapper">

    <resultMap id="BaseResultMap" type="com.ruoyi.system.api.domain.entity.biz.business.BusinessAccount">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="account" column="account" jdbcType="VARCHAR"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="businessId" column="business_id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,account,password,business_id,
        name,status,
        last_login_time,create_time,update_time
    </sql>

    <select id="getBusinessAccountDetailVOs" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO">
        SELECT
            ba.id,
            ba.account,
            ba.business_id,
            COALESCE(bu.NAME, parent_bu.NAME) AS NAME,
            COALESCE(bu.nick_name, parent_bu.nick_name) AS nick_name,
            bu.pic,
            bu.unionid,
            bu.seed_id,
            bu.external_user_id,
            bu.phone,
            ba.STATUS,
            ba.last_login_time,
            b.NAME AS businessName,
            b.STATUS AS businessStatus,
            b.create_time AS businessCreateTime,
            b.owner_account,
            b.is_proxy,
            b.phone_visible,
            b.customer_type,
            b.balance,
            b.use_balance,
            b.is_balance_lock,
            b.waiter_id,
            b.invoice_title_type,
            b.invoice_title,
            b.invoice_duty_paragraph,
            b.invoice_content,
            b.member_code,
            b.member_type,
            b.member_status,
            b.member_package_type,
            b.member_first_time,
            b.member_first_type,
            b.member_last_time,
            b.member_validity,
            b.is_exist_recent_order,
            ba.is_owner_account,
            bu.id AS bizUserId
        FROM business_account ba
            LEFT JOIN business b ON ba.business_id = b.id
            LEFT JOIN biz_user bu ON bu.id = ba.biz_user_id
            LEFT JOIN biz_user parent_bu ON parent_bu.id = ba.owner_account_biz_user_id
        <where>
            <if test="dto.ids != null and dto.ids.size() != 0">
                and ba.id in
                <foreach collection="dto.ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.userIds != null and dto.userIds.size() != 0">
                and bu.id in
                <foreach collection="dto.userIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.accounts != null and dto.accounts.size() != 0">
                and ba.account in
                <foreach collection="dto.accounts" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.memberStatusList != null and dto.memberStatusList.size() != 0">
                and b.member_status in
                <foreach collection="dto.memberStatusList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                and ba.business_id in
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessId != null"> and ba.business_id = #{dto.businessId}</if>
            <if test="dto.isOwnerAccount != null"> and ba.is_owner_account = #{dto.isOwnerAccount}</if>
            <if test="dto.name != null  and dto.name != ''"> and bu.name like concat('%', #{dto.name}, '%')</if>
            <if test="dto.account != null  and dto.account != ''"> and ba.account like concat('%', #{dto.account}, '%')</if>
            <if test="dto.nickName != null  and dto.nickName != ''"> and bu.nick_name like concat('%', #{dto.nickName}, '%')</if>
            <if test="dto.unionid != null  and dto.unionid != ''"> and bu.unionid = #{dto.unionid}</if>
            <if test="dto.status != null"> and ba.status = #{dto.status}</if>
            <if test="dto.ownerAccount != null  and dto.ownerAccount != ''"> and b.owner_account like concat('%', #{dto.ownerAccount}, '%')</if>
            <if test="dto.businessName != null  and dto.businessName != ''"> and b.name like concat('%', #{dto.businessName}, '%')</if>
            <if test="dto.memberCode != null  and dto.memberCode != ''"> and b.member_code like concat('%', UCASE(#{dto.memberCode}), '%')</if>
            <if test="dto.searchNameMemberCodeAccount != null and dto.searchNameMemberCodeAccount != ''"> and (
                 b.member_code like concat('%', UCASE(#{dto.searchNameMemberCodeAccount}), '%')
                 or b.name like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                 or bu.name like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                 or bu.nick_name like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                 or ba.account like concat('%', #{dto.searchNameMemberCodeAccount}, '%')
                 )</if>
            <if test="dto.seedIds != null and dto.seedIds.size() != 0">
                and bu.seed_id in
                <foreach collection="dto.seedIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="queryOne" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO">
        select ba.id,ba.account,ba.business_id,bu.name,ba.status,ba.last_login_time,ba.is_owner_account,ba.last_login_time,ba.is_owner_account,
               bu.nick_name,
               bu.pic,
               bu.id bizUserId,
               bu.unionid,
               bu.seed_id,
               bu.phone,
               bu.external_user_id,
               bu.status userStatus,
               bu.id bizUserId,
               bu.last_login_time userLastLoginTime,
               bu.connect_user_name
        from business_account ba
        inner join biz_user bu on ba.biz_user_id = bu.id
        <where>
            <if test="dto.businessId != null"> and ba.business_id = #{dto.businessId}</if>
            <if test="dto.isOwnerAccount != null"> and ba.is_owner_account = #{dto.isOwnerAccount}</if>
            <if test="dto.account != null  and dto.account != ''"> and ba.account = #{dto.account}</if>
            <if test="dto.account != null  and dto.account != ''"> and ba.account = #{dto.account}</if>
                <if test="dto.unionid != null  and dto.unionid != ''"> and bu.unionid = #{dto.unionid}</if>
            <if test="dto.phone != null  and dto.phone != ''"> and bu.phone = #{dto.phone}</if>
            <if test="dto.bizUserId != null and dto.bizUserId != ''"> and bu.id = #{dto.bizUserId}</if>
            <if test="dto.id != null"> and ba.id = #{dto.id}</if>
        </where>
    </select>

    <select id="queryList" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO">
        select ba.id,ba.account,ba.business_id,bu.name,ba.status,ba.last_login_time,ba.is_owner_account,ba.last_login_time,ba.is_owner_account,
        bu.nick_name,
        bu.pic,
        bu.unionid,
        bu.seed_id,
        bu.phone,
        bu.external_user_id,
        bu.status userStatus,
        bu.id bizUserId,
        bu.last_login_time userLastLoginTime
        from business_account ba
        inner join biz_user bu on ba.biz_user_id = bu.id
        <where>
            <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                and ba.business_id in
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.ids != null and dto.ids.size() != 0">
                and ba.id in
                <foreach collection="dto.ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.accountList != null and dto.accountList.size() != 0">
                and ba.account in
                <foreach collection="dto.accounts" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessId != null"> and ba.business_id = #{dto.businessId}</if>
            <if test="dto.name != null  and dto.name != ''"> and bu.name like concat('%', #{dto.name}, '%')</if>
            <if test="dto.account != null  and dto.account != ''"> and ba.account like concat('%', #{dto.account}, '%')</if>
            <if test="dto.nickName != null  and dto.nickName != ''"> and bu.nick_name like concat('%', #{dto.nickName}, '%')</if>
            <if test="dto.unionid != null  and dto.unionid != ''"> and bu.unionid = #{dto.unionid}</if>
            <if test="dto.status != null"> and ba.status = #{dto.status}</if>
            <if test="dto.userStatus != null"> and bu.status = #{dto.userStatus}</if>
            <if test="dto.isOwnerAccount != null"> and ba.is_owner_account = #{dto.isOwnerAccount}</if>
        </where>
    </select>

    <select id="list" resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO">
        select ba.id,ba.account,ba.business_id,bu.name,ba.status,ba.last_login_time,ba.is_owner_account,ba.last_login_time,ba.is_owner_account,
        bu.nick_name,
        bu.pic,
        bu.unionid,
        bu.seed_id,
        bu.phone,
        bu.external_user_id,
        bu.status userStatus,
        bu.id bizUserId,
        bu.last_login_time userLastLoginTime
        from business_account ba
        left join biz_user bu on ba.biz_user_id = bu.id
        <where>
            <if test="dto.businessIds != null and dto.businessIds.size() != 0">
                and ba.business_id in
                <foreach collection="dto.businessIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.ids != null and dto.ids.size() != 0">
                and ba.id in
                <foreach collection="dto.ids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.accountList != null and dto.accountList.size() != 0">
                and ba.account in
                <foreach collection="dto.accountList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.businessId != null">and ba.business_id = #{dto.businessId}</if>
            <if test="dto.isOwnerAccount != null">and ba.is_owner_account = #{dto.isOwnerAccount}</if>
        </where>
    </select>
    <select id="getmemberCodeByAccountId" resultType="java.lang.String">
        SELECT b.member_code  from business_account ba left join business b on ba.business_id = b.id
        <where>
            ba.id = #{businessAccountId}
        </where>
    </select>
    <select id="getBusinessManagerList" resultType="java.lang.String">
        select connect_user_name
        from business_account ba
                 left join biz_user bu on ba.biz_user_id = bu.id
        WHERE is_owner_account = #{type} and connect_user_name is not null
        GROUP by connect_user_name
    </select>
    <select id="writeReturnVisitAccount"
            resultType="com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO">
        select ba.id,ba.account,ba.business_id,bu.name,ba.status,ba.last_login_time,ba.is_owner_account,ba.last_login_time,ba.is_owner_account,
               bu.nick_name,
               bu.pic,
               bu.unionid,
               bu.seed_id,
               bu.phone,
               bu.external_user_id,
               bu.status userStatus,
               bu.id bizUserId,
               bu.last_login_time userLastLoginTime
        from business_account ba
                 inner join biz_user bu on ba.biz_user_id = bu.id
        where
            ba.business_id = #{businessId}
            AND bu.status = 0
        ORDER BY
            ba.is_owner_account DESC,
            ba.create_time DESC
    </select>
</mapper>
