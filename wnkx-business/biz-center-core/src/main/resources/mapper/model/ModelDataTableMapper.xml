<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.biz.model.mapper.ModelDataTableMapper">


    <select id="selectModelDataTableListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO">
    WITH
        <if test="dto.remoteListJson != null and dto.remoteListJson != '' ">
        remote AS (
            SELECT * FROM JSON_TABLE(
                #{dto.remoteListJson},
                '$[*]'
                COLUMNS (
                    id  BIGINT  PATH '$.id',
                    submitIntentionCount INT  PATH '$.submitIntentionCount',
                    intentionCount INT  PATH '$.intentionCount',
                    intentionOrderRate DECIMAL(12,4)  PATH '$.intentionOrderRate',
                    submitPreSelectCount INT  PATH '$.submitPreSelectCount',
                    preSelectCount INT  PATH '$.preSelectCount',
                    preSelectOrderRate DECIMAL(12,4)  PATH '$.preSelectOrderRate',
                    submitDispatchCount INT  PATH '$.submitDispatchCount',
                    dispatchCount INT  PATH '$.dispatchCount',
                    dispatchOrderRate DECIMAL(12,4)  PATH '$.dispatchOrderRate',
                    submitSelfSelectCount INT  PATH '$.submitSelfSelectCount',
                    selfSelectCount INT  PATH '$.selfSelectCount',
                    selfSelectOrderRate DECIMAL(12,4)  PATH '$.selfSelectOrderRate',
                    changeModelCount INT  PATH '$.changeModelCount',
                    submitModelCount INT  PATH '$.submitModelCount',
                    rejectOrderRate DECIMAL(12,4)  PATH '$.rejectOrderRate',
                    orderScheduledCount INT  PATH '$.orderScheduledCount',
                    waitPictureCount INT  PATH '$.waitPictureCount',
                    feedbackCount INT  PATH '$.feedbackCount',
                    overtimeCount INT  PATH '$.overtimeCount',
                    confirmReceiptCount INT  PATH '$.confirmReceiptCount',
                    overtimeRate DECIMAL(12,4)  PATH '$.overtimeRate',
                    afterSaleCount INT  PATH '$.afterSaleCount',
                    feedbackMaterialsCount INT  PATH '$.feedbackMaterialsCount',
                    afterSaleRate DECIMAL(12,4)  PATH '$.afterSaleRate',
                    completeCount INT  PATH '$.completeCount',
                    dropCount INT  PATH '$.dropCount',
                    cancelCount INT  PATH '$.cancelCount',
                    returnCount INT  PATH '$.returnCount'
                )
            ) t
        ),
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(10) != ''">
        mvr AS (
            SELECT
                model_id,
                COUNT(*)     AS caseCount
            FROM model_video_resource
            GROUP BY model_id
        ),
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and ( dto.customColumns.contains(11) != '' or dto.customColumns.contains(12) != '' )">
        tg AS (
            SELECT
                mt.model_id,
                SUM(t.category_id=1009 OR mt.dict_category_id = 1009)  AS tagCount,
                SUM(t.category_id=1008 OR mt.dict_category_id = 1008)  AS categoryCount
            FROM model_tag mt
                LEFT JOIN tag t ON t.id=mt.dict_id AND t.category_id IN (1008,1009)
            GROUP BY mt.model_id
        ),
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(14) != ''">
        fm AS (
            SELECT
                family_id,
                COUNT(*) AS familyMemberCount
            FROM model
            GROUP BY family_id
        ),
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(15) != ''">
        bacm AS (
            SELECT
                model_id,
                COUNT(*) AS collectCount
            FROM business_account_collect_model
            <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
                WHERE create_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
            </if>
            GROUP BY model_id
        ),
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(16) != ''">
        umb AS (
            SELECT
                model_id,
                COUNT(*) AS blacklistCount
            FROM user_model_blacklist
            <if test="dto.dataScopeTimeBegin != null and dto.dataScopeTimeEnd != null">
                WHERE create_time BETWEEN #{dto.dataScopeTimeBegin} AND #{dto.dataScopeTimeEnd}
            </if>
            GROUP BY model_id
        ),
        </if>
        sorted_models AS (
            SELECT
                m.id,
                ma.account,
                m.status_sort,
                m.cooperation_sort,
                m.update_time
        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(1) != ''">
                ,mp.user_id AS service_id
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(10) != ''">
                ,IFNULL(mvr.caseCount,0)AS caseCount
                ,m.video_last_update_time AS caseCountUpdateTime
        </if>
        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and ( dto.customColumns.contains(11) != '' or dto.customColumns.contains(12) != '' )">
                ,IFNULL(tg.tagCount,0)AS tagCount
                ,m.tag_last_update_time AS tagCountUpdateTime
                ,IFNULL(tg.categoryCount,0)AS categoryCount
                ,m.category_last_update_time AS categoryCountUpdateTime
        </if>
        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(15) != ''">
                ,IFNULL(bacm.collectCount,0)AS collectCount
        </if>

        <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(16) != ''">
                ,IFNULL(umb.blacklistCount,0)AS blacklistCount
        </if>

        <if test="dto.remoteListJson != null and dto.remoteListJson != '' ">
                ,IFNULL(r.submitIntentionCount,0)AS submitIntentionCount
                ,IFNULL(r.intentionCount,0)AS intentionCount
                ,IFNULL(r.intentionOrderRate,0)AS intentionOrderRate
                ,IFNULL(r.submitPreSelectCount,0)AS submitPreSelectCount
                ,IFNULL(r.preSelectCount,0)AS preSelectCount
                ,IFNULL(r.preSelectOrderRate,0)AS preSelectOrderRate
                ,IFNULL(r.submitDispatchCount,0)AS submitDispatchCount
                ,IFNULL(r.dispatchCount,0)AS dispatchCount
                ,IFNULL(r.dispatchOrderRate,0)AS dispatchOrderRate
                ,IFNULL(r.submitSelfSelectCount,0)AS submitSelfSelectCount
                ,IFNULL(r.selfSelectCount,0)AS selfSelectCount
                ,IFNULL(r.selfSelectOrderRate,0)AS selfSelectOrderRate
                ,IFNULL(r.changeModelCount,0)AS changeModelCount
                ,IFNULL(r.submitModelCount,0)AS submitModelCount
                ,IFNULL(r.rejectOrderRate,0)AS rejectOrderRate
                ,IFNULL(r.orderScheduledCount,0)AS orderScheduledCount
                ,IFNULL(r.waitPictureCount,0)AS waitPictureCount
                ,IFNULL(r.feedbackCount,0)AS feedbackCount
                ,IFNULL(r.overtimeCount,0)AS overtimeCount
                ,IFNULL(r.confirmReceiptCount,0)AS confirmReceiptCount
                ,IFNULL(r.overtimeRate,0)AS overtimeRate
                ,IFNULL(r.afterSaleCount,0)AS afterSaleCount
                ,IFNULL(r.feedbackMaterialsCount,0)AS feedbackMaterialsCount
                ,IFNULL(r.afterSaleRate,0)AS afterSaleRate
                ,IFNULL(r.completeCount,0)AS completeCount
                ,IFNULL(r.dropCount,0)AS dropCount
                ,IFNULL(r.cancelCount,0)AS cancelCount
                ,IFNULL(r.returnCount,0)AS returnCount
        </if>
            FROM model m
                LEFT JOIN model_account ma ON ma.model_id = m.id
            <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(1) != ''">
                LEFT JOIN model_person mp ON mp.model_id = m.id
            </if>
            <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(10) != ''">
                LEFT JOIN mvr ON mvr.model_id = m.id
            </if>
            <if test="dto.customColumns != null and dto.customColumns.size() > 0 and ( dto.customColumns.contains(11) != '' or dto.customColumns.contains(12) != '' )">
                LEFT JOIN tg ON tg.model_id = m.id
            </if>
            <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(15) != ''">
                LEFT JOIN bacm ON bacm.model_id = m.id
            </if>
            <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(16) != ''">
                LEFT JOIN umb ON umb.model_id = m.id
            </if>
            <if test="dto.remoteListJson != null and dto.remoteListJson != '' ">
                LEFT JOIN remote r ON r.id = m.id
            </if>
        <where>
            <if test="dto.keyword != null and dto.keyword != ''">
                AND (
                m.name LIKE concat('%', #{dto.keyword}, '%')
                OR ma.account LIKE concat('%', #{dto.keyword}, '%')
                )
            </if>

            <if test="dto.createTimeBegin != null and dto.createTimeEnd != null">
                AND m.create_time BETWEEN #{dto.createTimeBegin} AND #{dto.createTimeEnd}
            </if>

            <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.orderScheduledCountSections != null and dto.orderScheduledCountSections.size() >0 ">
                AND (
                <foreach collection="dto.orderScheduledCountSections" item="item" separator=" OR ">
                    IFNULL(r.orderScheduledCount,0) BETWEEN #{item.begin} AND #{item.end}
                </foreach>
                )
            </if>

            <if test="dto.serviceIds != null and dto.serviceIds.size() > 0 ">
                AND mp.user_id IN
                <foreach collection="dto.serviceIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.developerIds != null and dto.developerIds.size() > 0 ">
                AND m.developer_id IN
                <foreach collection="dto.developerIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.cooperations != null and dto.cooperations.size() > 0 ">
                AND m.cooperation IN
                <foreach collection="dto.cooperations" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.statuses != null and dto.statuses.size() > 0 ">
                AND m.status IN
                <foreach collection="dto.statuses" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.platforms != null and dto.platforms.size() > 0 ">
                AND m.platform IN
                <foreach collection="dto.platforms" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.types != null and dto.types.size() > 0 ">
                AND m.type IN
                <foreach collection="dto.types" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.nations != null and dto.nations.size() > 0 ">
                AND m.nation IN
                <foreach collection="dto.nations" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.sexes != null and dto.sexes.size() > 0 ">
                AND m.sex IN
                <foreach collection="dto.sexes" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.ageGroups != null and dto.ageGroups.size() > 0 ">
                AND m.age_group IN
                <foreach collection="dto.ageGroups" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.isWarningModel != null and dto.customColumns != null and dto.customColumns.size() > 0 ">
                <if test="dto.isWarningModel == 1">
                    <trim prefix="AND (" prefixOverrides="OR" suffix=")">
                        <if test="dto.customColumns.contains(10) != ''">
                            OR ( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, m.video_last_update_time, NOW() ) >= 3 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(11) != ''">
                            OR ( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, m.tag_last_update_time, NOW() ) >= 3 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(12) != ''">
                            OR ( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, m.category_last_update_time, NOW() ) >= 3 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(17) != ''">
                            OR ( CASE WHEN m.status = 0 AND IFNULL(r.intentionCount,0) > 10 AND IFNULL(r.intentionOrderRate,0) &lt; 0.5 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(18) != ''">
                            OR ( CASE WHEN m.status = 0 AND m.cooperation = 1 AND IFNULL(r.preSelectCount,0) > 10 AND IFNULL(r.preSelectOrderRate,0) &lt; 0.6 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(20) != ''">
                            OR ( CASE WHEN m.status = 0 AND m.cooperation = 1 AND IFNULL(r.selfSelectCount,0) > 10 AND IFNULL(r.selfSelectOrderRate,0) &lt; 0.5 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(21) != ''">
                            OR ( CASE WHEN m.status = 0 AND m.cooperation = 1 AND IFNULL(r.submitModelCount,0) > 10 AND IFNULL(r.rejectOrderRate,0) > 0.15 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(22) != ''">
                            OR (CASE WHEN m.status = 0 AND (( m.cooperation = 1 AND IFNULL(r.orderScheduledCount,0) &lt; 3 ) OR ( m.cooperation = 0 AND (IFNULL(r.orderScheduledCount,0) &lt;= 0 OR ( IFNULL(r.orderScheduledCount,0) > 5 AND #{dto.dataScopeTimeBegin} IS NOT NULL AND #{dto.dataScopeTimeEnd} IS NOT NULL) ) )) THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(23) != ''">
                            OR (CASE WHEN ( m.cooperation = 1 AND IFNULL(r.waitPictureCount,0) > 15 )OR ( m.cooperation = 0 AND IFNULL(r.waitPictureCount,0) >5 ) THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(25) != ''">
                            OR (CASE WHEN IFNULL(r.orderScheduledCount,0) >10 AND IFNULL(r.overtimeRate,0) > 0.2 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(26) != ''">
                            OR (CASE WHEN IFNULL(r.orderScheduledCount,0) > 10 AND IFNULL(r.afterSaleRate,0) > 0.15 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(28) != ''">
                            OR (CASE WHEN IFNULL(r.dropCount,0) >2 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(29) != ''">
                            OR (CASE WHEN IFNULL(r.cancelCount,0) > 1 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(30) != ''">
                            OR (CASE WHEN IFNULL(r.returnCount,0) > 1 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                    </trim>
                </if>
                <if test="dto.isWarningModel == 0">
                    <trim prefix="AND (" prefixOverrides="AND" suffix=")">
                        <if test="dto.customColumns.contains(10) != ''">
                            AND ( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, m.video_last_update_time, NOW() ) >= 3 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(11) != ''">
                            AND ( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, m.tag_last_update_time, NOW() ) >= 3 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(12) != ''">
                            AND ( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, m.category_last_update_time, NOW() ) >= 3 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(17) != ''">
                            AND ( CASE WHEN m.status = 0 AND IFNULL(r.intentionCount,0) > 10 AND IFNULL(r.intentionOrderRate,0) &lt; 0.5 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(18) != ''">
                            AND ( CASE WHEN m.status = 0 AND m.cooperation = 1 AND IFNULL(r.preSelectCount,0) > 10 AND IFNULL(r.preSelectOrderRate,0) &lt; 0.6 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(20) != ''">
                            AND ( CASE WHEN m.status = 0 AND m.cooperation = 1 AND IFNULL(r.selfSelectCount,0) > 10 AND IFNULL(r.selfSelectOrderRate,0) &lt; 0.5 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(21) != ''">
                            AND ( CASE WHEN m.status = 0 AND m.cooperation = 1 AND IFNULL(r.submitModelCount,0) > 10 AND IFNULL(r.rejectOrderRate,0) > 0.15 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(22) != ''">
                            AND (CASE WHEN m.status = 0 AND (( m.cooperation = 1 AND IFNULL(r.orderScheduledCount,0) &lt; 3 ) OR ( m.cooperation = 0 AND (IFNULL(r.orderScheduledCount,0) &lt;= 0 OR ( IFNULL(r.orderScheduledCount,0) > 5 AND #{dto.dataScopeTimeBegin} IS NOT NULL AND #{dto.dataScopeTimeEnd} IS NOT NULL) ) )) THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(23) != ''">
                            AND (CASE WHEN ( m.cooperation = 1 AND IFNULL(r.waitPictureCount,0) > 15 )OR ( m.cooperation = 0 AND IFNULL(r.waitPictureCount,0) >5 ) THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(25) != ''">
                            AND (CASE WHEN IFNULL(r.orderScheduledCount,0) >10 AND IFNULL(r.overtimeRate,0) > 0.2 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(26) != ''">
                            AND (CASE WHEN IFNULL(r.orderScheduledCount,0) > 10 AND IFNULL(r.afterSaleRate,0) > 0.15 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(28) != ''">
                            AND (CASE WHEN IFNULL(r.dropCount,0) >2 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(29) != ''">
                            AND (CASE WHEN IFNULL(r.cancelCount,0) > 1 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                        <if test="dto.customColumns.contains(30) != ''">
                            AND (CASE WHEN IFNULL(r.returnCount,0) > 1 THEN 1 ELSE 0 END) = #{dto.isWarningModel}
                        </if>
                    </trim>
                </if>
            </if>
        </where>
        )
            SELECT
                sm.id,
                sm.account,
                m.model_pic,
                m.NAME,
                m.create_time,
                m.STATUS
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(1) != ''">
                    ,sm.service_id
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(2) != ''">
                    ,m.developer_id
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(3) != ''">
                    ,m.sex
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(4) != ''">
                    ,m.nation
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(5) != ''">
                    ,m.platform
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(6) != ''">
                    ,m.type
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(7) != ''">
                    ,m.age_group
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(8) != ''">
                    ,m.cooperation
                    ,m.cooperation_score
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(9) != ''">
                    ,m.commission_unit
                    ,m.commission
                    ,ROUND(
                            m.commission * (
                                CASE
                                    WHEN m.commission_unit = '${@<EMAIL>}' THEN
                                        ${@<EMAIL>}
                                    WHEN m.commission_unit = '${@<EMAIL>}' THEN
                                        ${@<EMAIL>}
                                    WHEN m.commission_unit = '${@<EMAIL>}' THEN
                                        ${@<EMAIL>} ELSE 1
                                    END
                                ),
                            4
                    ) AS commissionSort
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(10) != ''">
                    ,sm.caseCount
                    ,sm.caseCountUpdateTime
                    ,( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, sm.caseCountUpdateTime, NOW() ) >= 3 THEN 1 ELSE 0 END) AS caseCountEarlyWarn
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(11) != ''">
                    ,sm.tagCount
                    ,sm.tagCountUpdateTime
                    ,( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, sm.tagCountUpdateTime, NOW() ) >= 3 THEN 1 ELSE 0 END) AS tagCountEarlyWarn
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(12) != ''">
                    ,sm.categoryCount
                    ,sm.categoryCountUpdateTime
                    ,( CASE WHEN m.status = 0 AND TIMESTAMPDIFF( MONTH, sm.categoryCountUpdateTime, NOW() ) >= 3 THEN 1 ELSE 0 END) AS categoryCountEarlyWarn
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(13) != ''">
                    ,m.have_snail_pic
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(14) != ''">
                    ,IFNULL(fm.familyMemberCount,0) AS familyMemberCount
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(15) != ''">
                    ,sm.collectCount
                </if>
                <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(16) != ''">
                    ,sm.blacklistCount
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(17) != ''">
                    ,sm.submitIntentionCount
                    ,sm.intentionCount
                    ,sm.intentionOrderRate
                    ,( CASE WHEN m.status = 0 AND sm.intentionCount > 10 AND sm.intentionOrderRate &lt; 0.5 THEN 1 ELSE 0 END) AS intentionOrderRateEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(18) != ''">
                    ,sm.submitPreSelectCount
                    ,sm.preSelectCount
                    ,sm.preSelectOrderRate
                    ,( CASE WHEN m.status = 0 AND m.cooperation = 1 AND sm.preSelectCount > 10 AND sm.preSelectOrderRate &lt; 0.6 THEN 1 ELSE 0 END) AS preSelectOrderRateEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(19) != ''">
                    ,sm.submitDispatchCount
                    ,sm.dispatchCount
                    ,sm.dispatchOrderRate
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(20) != ''">
                    ,sm.submitSelfSelectCount
                    ,sm.selfSelectCount
                    ,sm.selfSelectOrderRate
                    ,( CASE WHEN m.status = 0 AND m.cooperation = 1 AND sm.selfSelectCount > 10 AND sm.selfSelectOrderRate &lt; 0.5 THEN 1 ELSE 0 END) AS selfSelectOrderRateEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(21) != ''">
                    ,sm.changeModelCount
                    ,sm.submitModelCount
                    ,sm.rejectOrderRate
                    ,( CASE WHEN m.status = 0 AND m.cooperation = 1 AND sm.submitModelCount > 10 AND sm.rejectOrderRate > 0.15 THEN 1 ELSE 0 END) AS rejectOrderRateEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(22) != ''">
                    ,sm.orderScheduledCount
                    ,(CASE WHEN m.status = 0 AND (( m.cooperation = 1 AND sm.orderScheduledCount &lt; 3 ) OR ( m.cooperation = 0 AND (sm.orderScheduledCount &lt;= 0 OR ( sm.orderScheduledCount > 5 AND #{dto.dataScopeTimeBegin} IS NOT NULL AND #{dto.dataScopeTimeEnd} IS NOT NULL) ) )) THEN 1 ELSE 0 END) AS orderScheduledCountEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(23) != ''">
                    ,sm.waitPictureCount
                    ,(CASE WHEN ( m.cooperation = 1 AND sm.waitPictureCount > 15 )OR ( m.cooperation = 0 AND sm.waitPictureCount >5 ) THEN 1 ELSE 0 END) AS waitPictureCountEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(24) != ''">
                    ,sm.feedbackCount
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(25) != ''">
                    ,sm.overtimeCount
                    ,sm.confirmReceiptCount
                    ,sm.overtimeRate
                    ,(CASE WHEN sm.orderScheduledCount >10 AND sm.overtimeRate > 0.2 THEN 1 ELSE 0 END) AS overtimeRateEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(26) != ''">
                    ,sm.afterSaleCount
                    ,sm.feedbackMaterialsCount
                    ,sm.afterSaleRate
                    ,(CASE WHEN sm.orderScheduledCount > 10 AND sm.afterSaleRate > 0.15 THEN 1 ELSE 0 END) AS afterSaleRateEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(27) != ''">
                    ,sm.completeCount
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(28) != ''">
                    ,sm.dropCount
                    ,(CASE WHEN sm.dropCount >2 THEN 1 ELSE 0 END)AS dropCountEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(29) != ''">
                    ,sm.cancelCount
                    ,(CASE WHEN sm.cancelCount > 1 THEN 1 ELSE 0 END) AS cancelCountEarlyWarn
                </if>
                <if test="dto.remoteListJson != null and dto.remoteListJson != '' and dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(30) != ''">
                    ,sm.returnCount
                    ,(CASE WHEN sm.returnCount > 1 THEN 1 ELSE 0 END) AS returnCountEarlyWarn
                </if>
            FROM sorted_models sm
                JOIN model m ON m.id = sm.id
            <if test="dto.customColumns != null and dto.customColumns.size() > 0 and dto.customColumns.contains(14) != ''">
                LEFT JOIN fm ON fm.family_id = m.family_id
            </if>
            ORDER BY
            <if test="dto.sortColumnStr == null or dto.sortColumnStr == '' or dto.sortWay == null or dto.sortWay == '' ">
                sm.status_sort ASC ,sm.cooperation_sort DESC ,sm.update_time DESC ,sm.id ASC
            </if>
            <if test="dto.sortColumnStr != null and dto.sortColumnStr != '' and dto.sortWay != null and dto.sortWay != '' ">
                ${dto.sortColumnStr} ${dto.sortWay} ,sm.id ASC
            </if>
    </select>
    <select id="getServiceSelect"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableServiceUserVO">
        SELECT
            user_id AS serviceId,
            COUNT(*) AS modelCount
        FROM
            model_person
        WHERE
            user_id IS NOT NULL
        GROUP BY
            user_id
    </select>
    <select id="getDeveloperSelect"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableServiceUserVO">
        SELECT
            developer_id AS serviceId,
            COUNT(*) AS modelCount
        FROM
            model
        WHERE
            developer_id IS NOT NULL AND developer_id != 0
        GROUP BY
            developer_id
    </select>
    <select id="getModelDataTableDetailVO"
            resultType="com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableDetailVO">
        SELECT
            m.id,
            m.model_pic,
            m.`name`,
            ma.account,
            m.sex,
            m.age_group,
            m.nation,
            m.type,
            m.cooperation_score,
            m.platform,
            m.`status`,
            mt.end_time AS tripRecoveryTime,
            m.commission_unit,
            m.commission,
            COUNT( DISTINCT bacm.id ) AS collectCount,
            COUNT( DISTINCT umb.id ) AS blacklistCount,
            GROUP_CONCAT( DISTINCT CASE WHEN sm.id != m.id THEN sm.id END ) AS familyMemberIds,
            mp.user_id AS serviceId,
            m.create_time
        FROM
            model m
                LEFT JOIN business_account_collect_model bacm ON bacm.model_id = m.id
                LEFT JOIN user_model_blacklist umb ON umb.model_id = m.id
                LEFT JOIN model_travel mt ON mt.model_id = m.id
                JOIN model_account ma ON ma.model_id = m.id
                LEFT JOIN model sm ON sm.family_id = m.family_id
                LEFT JOIN model_person mp ON mp.model_id = m.id
        WHERE
            m.id = #{modelId}
        GROUP BY
            m.id
    </select>
</mapper>