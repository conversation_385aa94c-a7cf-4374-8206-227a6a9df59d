import com.wnkx.order.OrderApplication;
import com.wnkx.order.service.IOrderVideoMatchPreselectModelService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * <AUTHOR> 2025年08月04日 11:05:10
 */
@SpringBootTest(classes = OrderApplication.class)
@ActiveProfiles("dev")
public class test1 {
    @Resource
    private IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;



    @Test
    public void test1(){
        orderVideoMatchPreselectModelService.cleanupInvalidPreselectModels();
    }
}
