# 测试环境配置
spring:
  profiles:
    active: test
  
  # 数据源配置 - 使用内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    
  # Redis配置 - 使用嵌入式Redis或Mock
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 2000ms
    
# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.wnkx.order: DEBUG
    org.springframework: WARN
    root: INFO
