# 开发规范和规则

- XXL-Job定时任务开发规范：1.订单相关任务放在order-job模块，业务数据任务放在biz-job模块；2.Handler类命名使用业务名+Handler格式；3.必须使用XxlJobHelper记录日志和处理结果；4.任务方法需要完整的异常处理机制
- XXL-Job定时任务开发规范：1.订单相关任务放在order-job模块，业务数据任务放在biz-job模块；2.Handler类命名使用业务名+Handler格式；3.必须使用XxlJobHelper记录日志和处理结果；4.任务方法需要完整的异常处理机制
- 为项目添加了 Spring Boot Test 支持：1. 在 order-core/pom.xml 中添加了 spring-boot-starter-test 依赖；2. 创建了使用 @SpringBootTest 的测试类 OrderVideoMatchPreselectModelServiceImplSpringTest.java；3. 创建了测试配置文件 application-test.yml。现在项目同时支持 Spock 和 Spring Boot Test 两种测试方式。
